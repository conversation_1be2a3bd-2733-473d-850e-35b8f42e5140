import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { toast } from 'sonner';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  speed?: number;
  heading?: number;
}

interface AdaptiveGPSConfig {
  // Battery optimization settings
  batteryOptimized: boolean;
  // Movement detection settings
  movementThreshold: number; // meters
  stationaryTimeout: number; // milliseconds
  // Polling intervals (milliseconds)
  activeInterval: number;
  stationaryInterval: number;
  backgroundInterval: number;
  // Accuracy settings
  highAccuracyEnabled: boolean;
  maxAcceptableAccuracy: number; // meters
}

interface MovementState {
  isStationary: boolean;
  lastMovementTime: number;
  averageSpeed: number;
  distanceTraveled: number;
}

const DEFAULT_CONFIG: AdaptiveGPSConfig = {
  batteryOptimized: false,
  movementThreshold: 10, // 10 meters
  stationaryTimeout: 300000, // 5 minutes
  activeInterval: 10000, // 10 seconds when moving
  stationaryInterval: 60000, // 1 minute when stationary
  backgroundInterval: 300000, // 5 minutes in background
  highAccuracyEnabled: true,
  maxAcceptableAccuracy: 50, // 50 meters
};

export const useAdaptiveGPS = (config: Partial<AdaptiveGPSConfig> = {}) => {
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  
  const [location, setLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [movementState, setMovementState] = useState<MovementState>({
    isStationary: false,
    lastMovementTime: Date.now(),
    averageSpeed: 0,
    distanceTraveled: 0,
  });
  
  const watchIdRef = useRef<number | null>(null);
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  const lastLocationRef = useRef<LocationData | null>(null);
  const speedHistoryRef = useRef<number[]>([]);
  const isBackgroundRef = useRef(false);

  // Calculate distance between two points using Haversine formula
  const calculateDistance = useCallback((
    lat1: number, lon1: number, lat2: number, lon2: number
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }, []);

  // Detect movement and update movement state
  const updateMovementState = useCallback((newLocation: LocationData) => {
    const lastLocation = lastLocationRef.current;
    
    if (!lastLocation) {
      lastLocationRef.current = newLocation;
      return;
    }

    const distance = calculateDistance(
      lastLocation.latitude, lastLocation.longitude,
      newLocation.latitude, newLocation.longitude
    );

    const timeDiff = newLocation.timestamp - lastLocation.timestamp;
    const speed = timeDiff > 0 ? (distance / timeDiff) * 1000 : 0; // m/s

    // Update speed history (keep last 5 readings)
    speedHistoryRef.current.push(speed);
    if (speedHistoryRef.current.length > 5) {
      speedHistoryRef.current.shift();
    }

    const averageSpeed = speedHistoryRef.current.reduce((a, b) => a + b, 0) / speedHistoryRef.current.length;
    const isMoving = distance > finalConfig.movementThreshold || averageSpeed > 0.5; // 0.5 m/s threshold

    setMovementState(prev => ({
      isStationary: !isMoving && (Date.now() - prev.lastMovementTime) > finalConfig.stationaryTimeout,
      lastMovementTime: isMoving ? Date.now() : prev.lastMovementTime,
      averageSpeed,
      distanceTraveled: prev.distanceTraveled + distance,
    }));

    lastLocationRef.current = newLocation;
  }, [calculateDistance, finalConfig.movementThreshold, finalConfig.stationaryTimeout]);

  // Get current GPS position with adaptive settings
  const getCurrentPosition = useCallback((options?: PositionOptions): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      const adaptiveOptions: PositionOptions = {
        enableHighAccuracy: finalConfig.batteryOptimized ? false : finalConfig.highAccuracyEnabled,
        timeout: finalConfig.batteryOptimized ? 15000 : 10000,
        maximumAge: movementState.isStationary ? 120000 : 30000, // 2 min if stationary, 30s if moving
        ...options,
      };

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp,
            speed: position.coords.speed || undefined,
            heading: position.coords.heading || undefined,
          };

          // Only accept location if accuracy is acceptable
          if (locationData.accuracy <= finalConfig.maxAcceptableAccuracy) {
            updateMovementState(locationData);
            setLocation(locationData);
            setError(null);
            resolve(locationData);
          } else {
            const errorMsg = `GPS accuracy too low: ${locationData.accuracy}m`;
            setError(errorMsg);
            reject(new Error(errorMsg));
          }
        },
        (error) => {
          let errorMessage = 'GPS error';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'GPS permission denied';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'GPS position unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'GPS timeout';
              break;
          }
          setError(errorMessage);
          reject(new Error(errorMessage));
        },
        adaptiveOptions
      );
    });
  }, [finalConfig, movementState.isStationary, updateMovementState]);

  // Start adaptive GPS tracking
  const startTracking = useCallback(() => {
    if (isTracking) return;

    setIsTracking(true);
    setError(null);

    const updateLocation = async () => {
      try {
        await getCurrentPosition();
      } catch (error) {
        console.warn('GPS update failed:', error);
        // Don't stop tracking on individual failures
      }
    };

    // Initial location fetch
    updateLocation();

    // Set up adaptive polling interval
    const scheduleNext = () => {
      let interval = finalConfig.activeInterval;

      if (isBackgroundRef.current) {
        interval = finalConfig.backgroundInterval;
      } else if (movementState.isStationary) {
        interval = finalConfig.stationaryInterval;
      }

      intervalIdRef.current = setTimeout(() => {
        updateLocation().finally(scheduleNext);
      }, interval);
    };

    scheduleNext();
  }, [isTracking, getCurrentPosition, finalConfig, movementState.isStationary]);

  // Stop GPS tracking
  const stopTracking = useCallback(() => {
    setIsTracking(false);
    
    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
    }
    
    if (intervalIdRef.current) {
      clearTimeout(intervalIdRef.current);
      intervalIdRef.current = null;
    }
  }, []);

  // Handle page visibility changes for battery optimization
  useEffect(() => {
    const handleVisibilityChange = () => {
      isBackgroundRef.current = document.hidden;
      
      if (finalConfig.batteryOptimized && isTracking) {
        // Restart tracking with new interval when visibility changes
        stopTracking();
        setTimeout(startTracking, 1000);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [finalConfig.batteryOptimized, isTracking, startTracking, stopTracking]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopTracking();
    };
  }, [stopTracking]);

  // Battery optimization helper
  const enableBatteryOptimization = useCallback(() => {
    if (isTracking) {
      stopTracking();
      setTimeout(() => startTracking(), 1000);
    }
    toast.success('Battery optimization enabled. GPS polling reduced.');
  }, [isTracking, startTracking, stopTracking]);

  return {
    location,
    error,
    isTracking,
    movementState,
    getCurrentPosition,
    startTracking,
    stopTracking,
    enableBatteryOptimization,
    config: finalConfig,
  };
};
