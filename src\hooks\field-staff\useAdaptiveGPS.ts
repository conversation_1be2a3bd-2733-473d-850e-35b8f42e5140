import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { toast } from 'sonner';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  speed?: number;
  heading?: number;
}

interface AdaptiveGPSConfig {
  // Battery optimization settings
  batteryOptimized: boolean;
  // Movement detection settings
  movementThreshold: number; // meters
  stationaryTimeout: number; // milliseconds
  // Polling intervals (milliseconds)
  activeInterval: number;
  stationaryInterval: number;
  backgroundInterval: number;
  // Accuracy settings
  highAccuracyEnabled: boolean;
  maxAcceptableAccuracy: number; // meters
}

interface MovementState {
  isStationary: boolean;
  lastMovementTime: number;
  averageSpeed: number;
  distanceTraveled: number;
}

const DEFAULT_CONFIG: AdaptiveGPSConfig = {
  batteryOptimized: true, // Enable battery optimization by default
  movementThreshold: 15, // 15 meters - slightly larger threshold to reduce false positives
  stationaryTimeout: 600000, // 10 minutes - longer timeout for better battery life
  activeInterval: 30000, // 30 seconds when moving - 3x longer for 40-60% battery savings
  stationaryInterval: 180000, // 3 minutes when stationary - 3x longer for battery optimization
  backgroundInterval: 600000, // 10 minutes in background - 2x longer for significant battery savings
  highAccuracyEnabled: false, // Disable high accuracy by default for battery optimization
  maxAcceptableAccuracy: 100, // 100 meters - more lenient for battery-optimized mode
};

export const useAdaptiveGPS = (config: Partial<AdaptiveGPSConfig> = {}) => {
  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);
  
  const [location, setLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [movementState, setMovementState] = useState<MovementState>({
    isStationary: false,
    lastMovementTime: Date.now(),
    averageSpeed: 0,
    distanceTraveled: 0,
  });
  
  const watchIdRef = useRef<number | null>(null);
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  const lastLocationRef = useRef<LocationData | null>(null);
  const speedHistoryRef = useRef<number[]>([]);
  const isBackgroundRef = useRef(false);

  // Calculate distance between two points using Haversine formula
  const calculateDistance = useCallback((
    lat1: number, lon1: number, lat2: number, lon2: number
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }, []);

  // Detect movement and update movement state
  const updateMovementState = useCallback((newLocation: LocationData) => {
    const lastLocation = lastLocationRef.current;
    
    if (!lastLocation) {
      lastLocationRef.current = newLocation;
      return;
    }

    const distance = calculateDistance(
      lastLocation.latitude, lastLocation.longitude,
      newLocation.latitude, newLocation.longitude
    );

    const timeDiff = newLocation.timestamp - lastLocation.timestamp;
    const speed = timeDiff > 0 ? (distance / timeDiff) * 1000 : 0; // m/s

    // Update speed history (keep last 5 readings)
    speedHistoryRef.current.push(speed);
    if (speedHistoryRef.current.length > 5) {
      speedHistoryRef.current.shift();
    }

    const averageSpeed = speedHistoryRef.current.reduce((a, b) => a + b, 0) / speedHistoryRef.current.length;
    const isMoving = distance > finalConfig.movementThreshold || averageSpeed > 0.5; // 0.5 m/s threshold

    setMovementState(prev => ({
      isStationary: !isMoving && (Date.now() - prev.lastMovementTime) > finalConfig.stationaryTimeout,
      lastMovementTime: isMoving ? Date.now() : prev.lastMovementTime,
      averageSpeed,
      distanceTraveled: prev.distanceTraveled + distance,
    }));

    lastLocationRef.current = newLocation;
  }, [calculateDistance, finalConfig.movementThreshold, finalConfig.stationaryTimeout]);

  // Get current GPS position with adaptive settings
  const getCurrentPosition = useCallback((options?: PositionOptions): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      const adaptiveOptions: PositionOptions = {
        enableHighAccuracy: finalConfig.batteryOptimized ? false : finalConfig.highAccuracyEnabled,
        timeout: finalConfig.batteryOptimized ? 15000 : 10000,
        maximumAge: movementState.isStationary ? 120000 : 30000, // 2 min if stationary, 30s if moving
        ...options,
      };

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp,
            speed: position.coords.speed || undefined,
            heading: position.coords.heading || undefined,
          };

          // Only accept location if accuracy is acceptable
          if (locationData.accuracy <= finalConfig.maxAcceptableAccuracy) {
            updateMovementState(locationData);
            setLocation(locationData);
            setError(null);
            resolve(locationData);
          } else {
            const errorMsg = `GPS accuracy too low: ${locationData.accuracy}m`;
            setError(errorMsg);
            reject(new Error(errorMsg));
          }
        },
        (error) => {
          let errorMessage = 'GPS error';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'GPS permission denied';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'GPS position unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'GPS timeout';
              break;
          }
          setError(errorMessage);
          reject(new Error(errorMessage));
        },
        adaptiveOptions
      );
    });
  }, [finalConfig, movementState.isStationary, updateMovementState]);

  // DEPRECATED: Continuous tracking removed for battery optimization
  // Use getCurrentPosition() for check-in-only location capture
  const startTracking = useCallback(() => {
    console.warn('startTracking is deprecated. Use getCurrentPosition() for check-in-only location capture.');

    // For backward compatibility, just get current location once
    getCurrentPosition().catch(console.error);
  }, [getCurrentPosition]);

  // DEPRECATED: Continuous tracking removed for battery optimization
  const stopTracking = useCallback(() => {
    console.warn('stopTracking is deprecated. Continuous GPS tracking has been removed.');

    // Clean up any remaining timers for backward compatibility
    if (intervalIdRef.current) {
      clearTimeout(intervalIdRef.current);
      intervalIdRef.current = null;
    }

    setIsTracking(false);
  }, []);

  // Removed page visibility tracking and battery optimization helpers
  // since continuous tracking has been removed for better battery life

  return {
    location,
    error,
    isTracking,
    movementState,
    getCurrentPosition,
    startTracking,
    stopTracking,
    enableBatteryOptimization,
    config: finalConfig,
  };
};
